.\" Automatically generated by Pod::Man 2.22 (Pod::Simple 3.07)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is turned on, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.ie \nF \{\
.    de IX
.    tm Index:\\$1\t\\n%\t"\\$2"
..
.    nr % 0
.    rr F
.\}
.el \{\
.    de IX
..
.\}
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "SERIAL-CONSOLE 1"
.TH SERIAL-CONSOLE 1 "2010-09-22" "perl v5.10.1" "User Contributed Perl Documentation"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
serial\-console
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
serial-console [options]
.PP
Options:
.PP
.Vb 5
\&    \-h,\-\-help         Display brief help message
\&    \-v,\-\-verbose      Increase verbosity
\&    \-q,\-\-quiet        Decrease verbosity
\&    \-l,\-\-log FILE     Log output to file
\&    \-r,\-\-rcfile FILE  Modify specified bochsrc file
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\f(CW\*(C`serial\-console\*(C'\fR provides a virtual serial console for use with
Bochs.  Running \f(CW\*(C`serial\-console\*(C'\fR creates a pseudo-tty.  The master
side of this pty is made available to the user for interaction; the
slave device is written to the Bochs configuration file
(\f(CW\*(C`bochsrc.txt\*(C'\fR) for use by a subsequent Bochs session.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
.ie n .IP """serial\-console""" 4
.el .IP "\f(CWserial\-console\fR" 4
.IX Item "serial-console"
Create a virtual serial console for Bochs, modify \f(CW\*(C`bochsrc.txt\*(C'\fR
appropriately.
.ie n .IP """serial\-console \-r ../.bochsrc \-l serial.log""" 4
.el .IP "\f(CWserial\-console \-r ../.bochsrc \-l serial.log\fR" 4
.IX Item "serial-console -r ../.bochsrc -l serial.log"
Create a virtual serial console for Bochs, modify \f(CW\*(C`../.bochsrc\*(C'\fR
appropriately, log output to \f(CW\*(C`serial.log\*(C'\fR.
.SH "INVOCATION"
.IX Header "INVOCATION"
Before starting Bochs, run \f(CW\*(C`serial\-console\*(C'\fR in a different session
(e.g. a different xterm window).  When you subsequently start Bochs,
anything that the emulated machine writes to its serial port will
appear in the window running \f(CW\*(C`serial\-console\*(C'\fR, and anything typed in
the \f(CW\*(C`serial\-console\*(C'\fR window will arrive on the emulated machine's
serial port.
.PP
You do \fBnot\fR need to rerun \f(CW\*(C`serial\-console\*(C'\fR afresh for each Bochs
session.
.SH "OPTIONS"
.IX Header "OPTIONS"
.IP "\fB\-l,\-\-log \s-1FILE\s0\fR" 4
.IX Item "-l,--log FILE"
Log all output (i.e. everything that is printed in the
\&\f(CW\*(C`serial\-console\*(C'\fR window) to the specified file.
.IP "\fB\-r,\-\-rcfile \s-1FILE\s0\fR" 4
.IX Item "-r,--rcfile FILE"
Modify the specified bochsrc file.  The file will be updated to
contain the path to the slave side of the psuedo tty that we create.
The original file will be restored when \f(CW\*(C`serial\-console\*(C'\fR exits.  The
default is to modify the file \f(CW\*(C`bochsrc.txt\*(C'\fR in the current directory.
.Sp
To avoid modifying any bochsrc file, use \f(CW\*(C`\-\-norcfile\*(C'\fR.
