# -*- makefile -*- : Force emacs to use Makefile mode

# Build a position-independent executable, with relocations required
# only for data values.  Runtime relocations are applied by the
# prefix code.
#
CFLAGS		+= -mcmodel=medany -fpie
LDFLAGS		+= -pie --no-dynamic-linker -z combreloc

# Place explicitly zero-initialised variables in the .data section
# rather than in .bss, so that we can rely on their values even during
# parsing of the system memory map prior to relocation (and therefore
# prior to explicit zeroing of the .bss section).
#
CFLAGS		+= -fno-zero-initialized-in-bss

# Linker script
#
LDSCRIPT	= arch/riscv/scripts/sbi.lds

# Media types
#
MEDIA		+= sbi
MEDIA		+= lkrn

# Padded flash device images (e.g. for QEMU's -pflash option)
#
NON_AUTO_MEDIA	+= pf32
%.pf32 : %.sbi $(MAKEDEPS)
	$(Q)$(CP) $< $@
	$(Q)$(TRUNCATE) -s 32M $@
