/*
 * Copyright (C) 2009 <PERSON><PERSON> <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 *
 * You can also choose to distribute this program under the terms of
 * the Unmodified Binary Distribution Licence (as given in the file
 * COPYING.UBDL), provided that you have satisfied its requirements.
 */

FILE_LICENCE ( GPL2_OR_LATER_OR_UBDL )

	.section ".note.GNU-stack", "", @progbits
	.code16
	.arch i386

/****************************************************************************
 * Set/clear CF on the stack as appropriate, assumes stack is as it should
 * be immediately before IRET
 ****************************************************************************
 */
	.section ".text16", "ax", @progbits
	.globl patch_cf
patch_cf:
	pushw	%bp
	movw	%sp, %bp
	setc	8(%bp)	/* Set/reset CF; clears PF, AF, ZF, SF */
	popw	%bp
	ret
	.size patch_cf, . - patch_cf
