name: Coverity Scan

on:
  push:
    branches:
      - coverity_scan

jobs:
  submit:
    name: Submit
    runs-on: ubuntu-22.04
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Download Coverity Scan
        run: |
          curl --form token=${{ secrets.COVERITY_SCAN_TOKEN }} \
               --form project=${{ github.repository }} \
               --output coverity.tar.gz \
               https://scan.coverity.com/download/cxx/linux64
          mkdir -p /opt/coverity
          sudo tar xvzf coverity.tar.gz --strip 1 --directory /opt/coverity
      - name: Build via Coverity Scan
        run: |
          make -C src bin/deps
          /opt/coverity/bin/cov-build --dir cov-int make -C src bin/blib.a
      - name: Create submission
        run : |
          tar cvzf cov-int.tar.gz cov-int
      - name: Submit to Coverity Scan
        run: |
          curl --form token=${{ secrets.COVERITY_SCAN_TOKEN }} \
               --form email=${{ secrets.COVERITY_SCAN_EMAIL }} \
               --form file=@cov-int.tar.gz \
               --form version=${{ github.sha }} \
               --form description=${{ github.ref }} \
               https://scan.coverity.com/builds?project=${{ github.repository }}
